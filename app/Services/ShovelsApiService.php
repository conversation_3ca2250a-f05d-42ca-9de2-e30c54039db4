<?php

namespace App\Services;

use App\Services\HttpClientService;
use Carbon\Carbon;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Exceptions\HttpResponseException;

class ShovelsApiService
{

    private const BASE_URL = 'https://api.shovels.ai/v2';
    private const CONTRACTORS_ENDPOINT = '/contractors/search';

    private HttpClientService $httpClient;
    private string $apiKey;
    private ?string $lastRequestUrl = null;

    public function __construct()
    {
        // Initialize HTTP client with base URL and default headers
        $this->httpClient = new HttpClientService(
            self::BASE_URL,
            ['Accept' => 'application/json'],
            30 // 30 second timeout
        );
    }

    /**
     * Set the API key for authentication
     */
    public function setApiKey(string $apiKey): void
    {
        $this->apiKey = $apiKey;
    }

    /**
     * Test the API connection with a simple request
     */
    public function testApiConnection(): array
    {
        $testParams = [
            'permit_from' => '2024-01-01',
            'permit_to' => '2024-01-31',
            'geo_id' => 'US',
            'size' => 1,
        ];

        logger()->info('Testing Shovels API connection', [
            'test_params' => $testParams,
            'api_key_prefix' => substr($this->apiKey, 0, 8) . '...',
        ]);

        try {
            $response = $this->httpClient->get(
                self::CONTRACTORS_ENDPOINT,
                $testParams,
                ['X-API-Key' => $this->apiKey]
            );

            logger()->info('API test response', [
                'response_keys' => array_keys($response),
                'items_count' => isset($response['items']) ? count($response['items']) : 0,
            ]);

            return $response;
        } catch (\Exception $e) {
            logger()->error('API test failed', [
                'error' => $e->getMessage(),
                'error_class' => get_class($e),
            ]);
            throw $e;
        }
    }

    /**
     * Get the last request URL for logging purposes
     */
    public function getLastRequestUrl(): ?string
    {
        return $this->lastRequestUrl;
    }

    /**
     * Search for contractors using company name and optionally website
     *
     * @param string $companyName
     * @param string|null $companyWebsite
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array|null
     * @throws RequestException|ConnectionException|HttpResponseException
     */
    public function searchContractors(
        string $companyName,
        ?string $companyWebsite,
        Carbon $startDate,
        Carbon $endDate
    ): ?array {
        // Build query parameters according to Shovels API documentation
        // Note: geo_id is required according to API docs, using a broad search
        $queryParams = [
            'permit_from' => $startDate->format('Y-m-d'),
            'permit_to' => $endDate->format('Y-m-d'),
            'contractor_name' => $companyName,
            'geo_id' => 'US', // Try using US as a broad geographic filter
            'size' => 100, // Maximum results per page
        ];

        // Add website parameter only if provided
        $cleanWebsite = null;
        if ($companyWebsite) {
            $cleanWebsite = $this->cleanWebsiteUrl($companyWebsite);
            $queryParams['contractor_website'] = $cleanWebsite;
        }

        // Store the request URL for logging
        $this->lastRequestUrl = self::BASE_URL . self::CONTRACTORS_ENDPOINT . '?' . http_build_query($queryParams);

        // Verbose logging
        logger()->info('Shovels API Request Details', [
            'company_name' => $companyName,
            'original_website' => $companyWebsite,
            'clean_website' => $cleanWebsite,
            'using_website_filter' => !is_null($cleanWebsite),
            'query_params' => $queryParams,
            'full_url' => $this->lastRequestUrl,
            'api_key_prefix' => substr($this->apiKey, 0, 8) . '...',
        ]);

        try {
            $response = $this->httpClient->get(
                self::CONTRACTORS_ENDPOINT,
                $queryParams,
                ['X-API-Key' => $this->apiKey]
            );

            // Verbose response logging
            logger()->info('Shovels API Response', [
                'response_keys' => array_keys($response),
                'items_count' => isset($response['items']) ? count($response['items']) : 0,
                'response_size' => strlen(json_encode($response)),
                'full_response' => $response,
            ]);

            // Check if we have results
            if (isset($response['items']) && !empty($response['items'])) {
                logger()->info('Found contractor match', [
                    'contractor_id' => $response['items'][0]['id'] ?? 'unknown',
                    'contractor_name' => $response['items'][0]['name'] ?? 'unknown',
                    'permit_count' => $response['items'][0]['permit_count'] ?? 0,
                ]);
                // Return the first matching contractor
                return $response['items'][0];
            }

            logger()->warning('No contractors found in response', [
                'company_name' => $companyName,
                'clean_website' => $cleanWebsite ?? 'not provided',
                'response_structure' => array_keys($response),
            ]);

            // Try alternative search strategies
            return $this->tryAlternativeSearches($companyName, $cleanWebsite, $startDate, $endDate);

        } catch (HttpResponseException $e) {
            // Handle HTTP response errors (4xx, 5xx status codes)
            $response = $e->getResponse();
            $statusCode = $response->getStatusCode();
            $responseBody = $response->getContent();

            logger()->error('Shovels API HTTP error response', [
                'company_name' => $companyName,
                'website' => $cleanWebsite ?? 'not provided',
                'status_code' => $statusCode,
                'response_body' => $responseBody,
                'url' => $this->lastRequestUrl,
                'error_message' => $e->getMessage(),
            ]);

            // For 4xx errors, return null instead of throwing (these are often "not found" cases)
            if ($statusCode >= 400 && $statusCode < 500) {
                logger()->info('Treating 4xx response as "no results found"', [
                    'status_code' => $statusCode,
                    'company_name' => $companyName,
                ]);
                return null;
            }

            // For 5xx errors, re-throw as these are server errors
            throw $e;

        } catch (RequestException | ConnectionException $e) {
            logger()->error('Shovels API request failed', [
                'company_name' => $companyName,
                'website' => $cleanWebsite ?? 'not provided',
                'error' => $e->getMessage(),
                'error_class' => get_class($e),
                'error_code' => $e->getCode(),
                'url' => $this->lastRequestUrl,
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Try alternative search strategies if initial search fails
     */
    private function tryAlternativeSearches(
        string $companyName,
        ?string $cleanWebsite,
        Carbon $startDate,
        Carbon $endDate
    ): ?array {
        $strategies = [
            // Strategy 1: Remove geo_id constraint
            [
                'name' => 'No geo constraint',
                'params' => [
                    'permit_from' => $startDate->format('Y-m-d'),
                    'permit_to' => $endDate->format('Y-m-d'),
                    'contractor_name' => $companyName,
                    'size' => 100,
                ]
            ],
            // Strategy 2: Broader date range (last 6 months)
            [
                'name' => 'Broader date range',
                'params' => [
                    'permit_from' => $startDate->subMonths(5)->format('Y-m-d'),
                    'permit_to' => $endDate->format('Y-m-d'),
                    'contractor_name' => $companyName,
                    'geo_id' => 'US',
                    'size' => 100,
                ]
            ],
            // Strategy 3: Partial name match (first word only)
            [
                'name' => 'Partial name match',
                'params' => [
                    'permit_from' => $startDate->format('Y-m-d'),
                    'permit_to' => $endDate->format('Y-m-d'),
                    'contractor_name' => explode(' ', $companyName)[0],
                    'geo_id' => 'US',
                    'size' => 100,
                ]
            ]
        ];


        foreach ($strategies as $strategy) {
            logger()->info("Trying alternative search strategy: {$strategy['name']}", [
                'params' => $strategy['params']
            ]);

            try {
                $response = $this->httpClient->get(
                    self::CONTRACTORS_ENDPOINT,
                    $strategy['params'],
                    ['X-API-Key' => $this->apiKey]
                );

                if (isset($response['items']) && !empty($response['items'])) {
                    logger()->info("Alternative strategy '{$strategy['name']}' found results", [
                        'count' => count($response['items'])
                    ]);
                    return $response['items'][0];
                }
            } catch (\Exception $e) {
                logger()->warning("Alternative strategy '{$strategy['name']}' failed", [
                    'error' => $e->getMessage()
                ]);
            }
        }

        return null;
    }

    /**
     * Clean website URL by removing protocol and www
     */
    private function cleanWebsiteUrl(string $website): string
    {
        // Remove protocol (http:// or https://)
        $cleaned = preg_replace('/^https?:\/\//', '', $website);

        // Remove www. prefix if present
        $cleaned = preg_replace('/^www\./', '', $cleaned);

        // Remove trailing slash
        $cleaned = rtrim($cleaned, '/');

        return $cleaned;
    }

    /**
     * Search for contractors by geographic location
     *
     * @param string $geoId Geographic identifier (zip code, city, county, etc.)
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param array $permitTags Industry tags to filter permits (optional)
     * @return array
     * @throws RequestException|ConnectionException|HttpResponseException
     */
    public function searchContractorsByLocation(
        string $geoId,
        Carbon $startDate,
        Carbon $endDate,
        array $permitTags = []
    ): array {
        $queryParams = [
            'permit_from' => $startDate->format('Y-m-d'),
            'permit_to' => $endDate->format('Y-m-d'),
            'geo_id' => $geoId,
            'size' => 100, // Maximum results per page
        ];

        // Add permit tags if provided
        if (!empty($permitTags)) {
            $queryParams['permit_tags'] = $permitTags;
        }

        // Store the request URL for logging
        $this->lastRequestUrl = self::BASE_URL . self::CONTRACTORS_ENDPOINT . '?' . http_build_query($queryParams);

        logger()->info('Shovels API Location Search', [
            'geo_id' => $geoId,
            'date_range' => "{$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}",
            'permit_tags' => $permitTags,
            'permit_tags_count' => count($permitTags),
            'query_params' => $queryParams,
            'full_url' => $this->lastRequestUrl,
        ]);

        try {
            $response = $this->httpClient->get(
                self::CONTRACTORS_ENDPOINT,
                $queryParams,
                ['X-API-Key' => $this->apiKey]
            );

            logger()->info('Shovels API Location Search Response', [
                'http_status' => 200, // HttpClientService only returns data on success
                'response_keys' => array_keys($response),
                'items_count' => isset($response['items']) ? count($response['items']) : 0,
                'pagination_type' => $response['pagination_type'] ?? 'unknown',
                'size' => $response['size'] ?? 0,
                'full_response' => $response, // Log full response for debugging
            ]);

            // Return all contractors found
            return $response['items'] ?? [];

        } catch (HttpResponseException $e) {
            $response = $e->getResponse();
            $statusCode = $response->getStatusCode();
            $responseBody = $response->getContent();

            logger()->error('Shovels API Location Search HTTP error', [
                'geo_id' => $geoId,
                'status_code' => $statusCode,
                'response_body' => $responseBody,
                'url' => $this->lastRequestUrl,
            ]);

            // For 4xx errors, return empty array instead of throwing
            if ($statusCode >= 400 && $statusCode < 500) {
                logger()->info('Treating 4xx response as "no results found"', [
                    'status_code' => $statusCode,
                    'geo_id' => $geoId,
                ]);
                return [];
            }

            throw $e;

        } catch (RequestException | ConnectionException $e) {
            logger()->error('Shovels API Location Search failed', [
                'geo_id' => $geoId,
                'error' => $e->getMessage(),
                'error_class' => get_class($e),
                'url' => $this->lastRequestUrl,
            ]);

            throw $e;
        }
    }
}
