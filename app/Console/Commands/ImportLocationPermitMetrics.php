<?php

namespace App\Console\Commands;

use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\ShovelsApiService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ImportLocationPermitMetrics extends Command
{
    // a1mZ2nbVrfBN1e-Io9lKDnusPGFCEETFNSM59tXOhZc

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-location-permit-metrics
                            {api_key : Shovels API key for authentication}
                            {geo_id : Geographic ID (zip code, city, county, etc.)}
                            {--date-from= : Start date (YYYY-MM-DD), defaults to last month}
                            {--date-to= : End date (YYYY-MM-DD), defaults to today}
                            {--permit-tags=* : Industry tags to filter permits (e.g., solar, hvac, reroof)}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Search for permits by location and industry tags, then match contractors to companies in database via website';

    protected ShovelsApiService $shovelsApiService;

    public function __construct(ShovelsApiService $shovelsApiService)
    {
        parent::__construct();
        $this->shovelsApiService = $shovelsApiService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $apiKey = $this->argument('api_key');
        $geoId = $this->argument('geo_id');
        $permitTags = $this->option('permit-tags');

        // Set API key for the service
        $this->shovelsApiService->setApiKey($apiKey);

        // Calculate date range
        $endDate = $this->option('date-to')
            ? Carbon::parse($this->option('date-to'))
            : Carbon::now();

        $startDate = $this->option('date-from')
            ? Carbon::parse($this->option('date-from'))
            : Carbon::now()->subMonths(3); // Look back 3 months for more data

        $this->info("Searching for permits in location: {$geoId}");
        $this->info("Date range: {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");

        if (!empty($permitTags)) {
            $this->info("Permit tags filter: " . implode(', ', $permitTags));
        } else {
            $this->info("Permit tags filter: None (all industries)");
        }

        try {
            // Search for contractors by location
            $this->info('Making API request to Shovels...');
            $contractors = $this->shovelsApiService->searchContractorsByLocation($geoId, $startDate, $endDate, $permitTags);

            $this->info('API request completed.');
            $this->info('Last request URL: ' . $this->shovelsApiService->getLastRequestUrl());

            if (empty($contractors)) {
                $this->warn('No contractors found for the specified location and date range.');
                $this->info('This could mean:');
                $this->info('  - No permits were filed in this location during the date range');
                $this->info('  - The geo_id format is not recognized by the API');
                $this->info('  - The date range is too narrow');
                $this->info('');
                $this->info('Try:');
                $this->info('  - A broader date range (e.g., --date-from="2024-01-01")');
                $this->info('  - A different geo_id format (zip code, city name, county)');
                $this->info('  - Check the logs for the actual API response');
                return self::SUCCESS;
            }

            $this->info("Found {" . count($contractors) . "} contractors. Attempting to match with companies...");

            $matchedCount = 0;
            $processedCount = 0;

            foreach ($contractors as $contractor) {
                $processedCount++;
                $this->info("Processing contractor {$processedCount}/" . count($contractors) . ": " . ($contractor['name'] ?? 'Unknown'));

                // Try to match contractor to company
                $company = $this->matchContractorToCompany($contractor);

                if ($company) {
                    $this->info("✓ Matched to company: {$company->name} (ID: {$company->id})");

                    // Store the permit metrics data
                    $this->storeCompanyMetrics($company, $contractor, $geoId);
                    $matchedCount++;
                } else {
                    if ($contractor['website'] || $contractor['primary_email']) {
                        $this->info("  Creating new prospect for: " . ($contractor['name'] ?? 'Unknown'));
                        $newBuyerProspect = $this->createNewBuyerProspect($contractor, $permitTags);
                        $this->info("  Created new prospect: {$newBuyerProspect->company_name} (ID: {$newBuyerProspect->id})");
                    }

                    $this->warn("✗ No matching company found for contractor: " . ($contractor['name'] ?? 'Unknown'));
                }
            }

            $this->info("\nImport completed:");
            $this->info("✓ Contractors processed: {$processedCount}");
            $this->info("✓ Companies matched: {$matchedCount}");
            $this->info("✗ No matches: " . ($processedCount - $matchedCount));

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("Error during import: " . $e->getMessage());
            $this->error("Exception class: " . get_class($e));
            return self::FAILURE;
        }
    }

    private function createNewBuyerProspect(array $contractor, array $permitTags): ?NewBuyerProspect
    {
        return NewBuyerProspect::query()->create([
            NewBuyerProspect::FIELD_COMPANY_NAME => $contractor['name'] ?? null,
            NewBuyerProspect::FIELD_COMPANY_WEBSITE => $contractor['website'] ?? null,
            NewBuyerProspect::FIELD_STATUS => ProspectStatus::INITIAL,
            NewBuyerProspect::FIELD_SOURCE => ProspectSource::REGISTRATION,
            NewBuyerProspect::FIELD_INDUSTRY_SERVICE_IDS => $permitTags->map(fn($tag) => $this->mapPermitTageToIndustryService($tag))->toArray(),
            NewBuyerProspect::FIELD_SOURCE_DATA => $contractor,
            NewBuyerProspect::FIELD_COMPANY_PHONE => $contractor['primary_phone'] ?? null,
            NewBuyerProspect::FIELD_ORDINAL_VALUE => 0
        ]);
    }

    private function mapPermitTageToIndustryService(string $permitTag): int
    {
        return match ($permitTag) {
            'solar' => 1,
            'roofing' => 5,
            'windows' => 21,
            'siding' => 22,
            'hvac' => 15,
            'bathrooms' => 30,
            'remodeling' => 30,
            default => 1,
        };
    }
    /**
     * Attempt to match a contractor to a company in the database
     */
    private function matchContractorToCompany(array $contractor): ?Company
    {
        // Extract contractor details
        $contractorName = $contractor['name'] ?? null;
        $contractorWebsite = $contractor['website'] ?? null;
        $contractorEmail = $contractor['primary_email'] ?? $contractor['email'] ?? null;

        $this->info("  Contractor details:");
        $this->info("    Name: " . ($contractorName ?? 'N/A'));
        $this->info("    Website: " . ($contractorWebsite ?? 'N/A'));
        $this->info("    Email: " . ($contractorEmail ?? 'N/A'));

        // Strategy 1: Match by cleaned website/LinkedIn URL
        if ($contractorWebsite) {
            $cleanedContractorWebsite = $this->cleanWebsiteUrl($contractorWebsite);
            $this->info("    Cleaned Website: {$cleanedContractorWebsite}");

            $company = Company::where(function($query) use ($cleanedContractorWebsite) {
                $query->whereRaw('LOWER(TRIM(TRAILING "/" FROM REPLACE(REPLACE(website, "https://", ""), "http://", ""))) = ?', [strtolower($cleanedContractorWebsite)])
                      ->orWhereRaw('LOWER(TRIM(TRAILING "/" FROM REPLACE(REPLACE(website_verified_url, "https://", ""), "http://", ""))) = ?', [strtolower($cleanedContractorWebsite)]);
            })->first();

            if ($company) {
                $this->info("    ✓ Matched by website");
                return $company;
            }
        }

        // Strategy 2: Match by email domain
        if ($contractorEmail && str_contains($contractorEmail, '@')) {
            $emailDomain = strtolower(substr($contractorEmail, strpos($contractorEmail, '@') + 1));
            $this->info("    Email domain: {$emailDomain}");

            $company = Company::where(function($query) use ($emailDomain) {
                $query->whereRaw('LOWER(TRIM(TRAILING "/" FROM REPLACE(REPLACE(website, "https://", ""), "http://", ""))) = ?', [$emailDomain])
                      ->orWhereRaw('LOWER(TRIM(TRAILING "/" FROM REPLACE(REPLACE(website_verified_url, "https://", ""), "http://", ""))) = ?', [$emailDomain]);
            })->first();

            if ($company) {
                $this->info("    ✓ Matched by email domain");
                return $company;
            }
        }

        // Strategy 3: Fuzzy match by name (exact match first, then partial)
        if ($contractorName) {
            // Exact name match
            $company = Company::whereRaw('LOWER(name) = ?', [strtolower($contractorName)])->first();
            if ($company) {
                $this->info("    ✓ Matched by exact name");
                return $company;
            }
        }

        return null;
    }

    /**
     * Clean website URL for comparison
     */
    private function cleanWebsiteUrl(string $url): string
    {
        // Remove protocol
        $cleaned = preg_replace('/^https?:\/\//', '', $url);

        // Remove www
        $cleaned = preg_replace('/^www\./', '', $cleaned);

        // Remove trailing slash
        $cleaned = rtrim($cleaned, '/');

        // For LinkedIn URLs, extract the company identifier
        if (str_contains($cleaned, 'linkedin.com/company/')) {
            $cleaned = str_replace('linkedin.com/company/', '', $cleaned);
            $cleaned = explode('/', $cleaned)[0]; // Take only the company identifier
        }

        return strtolower($cleaned);
    }

    /**
     * Store company metrics data
     */
    private function storeCompanyMetrics(Company $company, array $contractorData, string $geoId): void
    {
        // Add metadata to the contractor data
        $metricsData = array_merge($contractorData, [
            'search_geo_id' => $geoId,
            'search_date' => Carbon::now()->toISOString(),
            'match_strategy' => 'location_based_search'
        ]);

        CompanyMetric::create([
            CompanyMetric::FIELD_COMPANY_ID => $company->id,
            CompanyMetric::FIELD_SOURCE => CompanyMetricSources::SHOVELS->value,
            CompanyMetric::FIELD_REQUEST_TYPE => CompanyMetricRequestTypes::PERMIT_METRICS->value,
            CompanyMetric::FIELD_REQUEST_URL => $this->shovelsApiService->getLastRequestUrl(),
            CompanyMetric::FIELD_REQUEST_RESPONSE => $metricsData,
        ]);
    }
}
