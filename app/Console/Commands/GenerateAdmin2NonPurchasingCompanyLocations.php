<?php

namespace App\Console\Commands;

use App\Models\Locations\USLocation;
use App\Models\Odin\Address;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\NonPurchasingCompanyLocation;
use App\Models\ServiceRadius;
use App\Models\USZipCode;
use Illuminate\Console\Command;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Laravel\Prompts\Progress;
use PDO;
use function Laravel\Prompts\clear;
use function Laravel\Prompts\progress;

class GenerateAdmin2NonPurchasingCompanyLocations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:non-purchasing-locations {--chunk-size=100 : The number of records per batch} {--insert-limit=10000 : Maximum rows to insert at once } {--disable-emulate : disabled prepare bindings emulation } {--force : Run the query even if the table is not empty}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate non-purchasing locations to A2 table';

    private string $modelName = 'Locations';
    private int $chunkSize = 100;
    private int $insertLimit = 10000;
    private bool $forceInsert = false;
    private bool $emulate = true;
    private Progress $progressBar;

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->chunkSize = (int)$this->option('chunk-size') ?: 100;
        $this->insertLimit = (int)$this->option('insert-limit') ?: 10000;
        if ($this->option('disable-emulate'))
            $this->emulate = false;
        $this->forceInsert = (int)$this->option('force') ?? $this->forceInsert;

        $count = DB::table(CompanyLocation::TABLE)
            ->leftJoin(NonPurchasingCompanyLocation::TABLE, NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_COMPANY_LOCATION_ID, CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ID)
            ->whereNull(NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_COMPANY_LOCATION_ID)
            ->count();

        if (!$count) {
            $this->line("Company Locations table is empty.");

            return Command::FAILURE;
        }

        // Disabled for now due to issues
//        if (NonPurchasingCompanyLocation::query()->count()) {
//            if ($this->forceInsert) {
//                NonPurchasingCompanyLocation::query()->truncate();
//            }
//            else {
//                $this->error(NonPurchasingCompanyLocation::TABLE . ' table is not empty, aborting.');
//                $this->warn('Run the command with the --force option to truncate and re-generate');
//                return Command::FAILURE;
//            }
//        }

        clear();
        $chunks = ceil($count / $this->chunkSize);
        $this->info("Processing $count $this->modelName in $chunks chunks...\n");
        $this->progressBar = progress("Processing $this->modelName chunks", $chunks);
        $this->progressBar->start();

        $start = microtime(true);
        if ($this->generateLegacyNonPurchasingLocations()) {
            $this->progressBar->finish();
            $count = NonPurchasingCompanyLocation::query()->count();
            $finish = round(microtime(true) - $start);
            $this->info("\n$count records inserted successfully in {$finish}s.\n");

            return Command::SUCCESS;
        }

        return Command::FAILURE;
    }

    private function generateLegacyNonPurchasingLocations(): bool
    {
        DB::disableQueryLog();
        if ($this->emulate)
            DB::connection()->getPdo()->setAttribute(PDO::ATTR_EMULATE_PREPARES, true);

        return DB::table(CompanyLocation::TABLE)
            ->select(CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ID)
            ->leftJoin(NonPurchasingCompanyLocation::TABLE, NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_COMPANY_LOCATION_ID, CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ID)
            ->whereNull(NonPurchasingCompanyLocation::TABLE .'.'. NonPurchasingCompanyLocation::FIELD_COMPANY_LOCATION_ID)
            ->orderBy(CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_ID)
            ->chunk($this->chunkSize, function (Collection $companyLocationChunk) {
                $ids = $companyLocationChunk->pluck(CompanyLocation::FIELD_ID)->toArray();

                $query = DB::table(CompanyLocation::TABLE)
                    ->select([
                        DB::raw(CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_ID . ' as company_location_id'),
                        CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_COMPANY_ID,
                        DB::raw('city_locations.' . USLocation::FIELD_ID . ' as city_location_id'),
                        DB::raw('county_locations.' . USLocation::FIELD_ID . ' as county_location_id'),
                        DB::raw('state_location.' . USLocation::FIELD_ID . ' as state_location_id'),
                    ])->distinct()
                    ->join(Address::TABLE, Address::TABLE . '.' . Address::FIELD_ID, CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_ADDRESS_ID)
                    ->join(USLocation::TABLE, USLocation::TABLE . '.' . USLocation::FIELD_ID, Address::TABLE . '.' . Address::FIELD_ZIP_CODE_LOCATION_ID)
                    ->join(USLocation::TABLE . ' as state_location', fn(JoinClause $join) =>
                        $join->on('state_location.' . USLocation::FIELD_STATE_ABBREVIATION, USLocation::TABLE . '.' . USLocation::FIELD_STATE_ABBREVIATION)
                            ->where('state_location.' . USLocation::FIELD_TYPE, USLocation::TYPE_STATE)
                    )->leftJoin(ServiceRadius::TABLE, ServiceRadius::TABLE . '.' . ServiceRadius::FIELD_LOCATION_ID, 'state_location.' . USLocation::FIELD_ID)
                    ->join(USZipCode::TABLE . ' as central_zip_code', fn(JoinClause $join) =>
                        $join->on('central_zip_code.' . USZipCode::FIELD_ZIP_CODE, USLocation::TABLE . '.' . USLocation::FIELD_ZIP_CODE)
                            ->where('central_zip_code.' . USZipCode::FIELD_ZIP_TYPE, USZipCode::DEFAULT_ZIP_TYPE)
                            ->where('central_zip_code.' . USZipCode::FIELD_CITY_TYPE, USZipCode::DEFAULT_CITY_TYPE)
                    )->join(USZipCode::TABLE, fn(JoinClause $join) =>
                        $join->on(USZipCode::TABLE . '.' . USZipCode::FIELD_STATE_ABBR, USLocation::TABLE . '.' . USLocation::FIELD_STATE_ABBREVIATION)
                            ->where(USZipCode::TABLE . '.' . USZipCode::FIELD_ZIP_TYPE, USZipCode::DEFAULT_ZIP_TYPE)
                            ->where(USZipCode::TABLE . '.' . USZipCode::FIELD_CITY_TYPE, USZipCode::DEFAULT_CITY_TYPE)
                    )->join(USLocation::TABLE . ' as zip_locations', fn(JoinClause $join) =>
                        $join->on('zip_locations.' . USLocation::FIELD_ZIP_CODE, USZipCode::TABLE . '.' . USZipCode::FIELD_ZIP_CODE)
                            ->where('zip_locations.' . USLocation::FIELD_TYPE, USLocation::TYPE_ZIP_CODE)
                            ->where(USZipCode::TABLE . '.' . USZipCode::FIELD_CITY_TYPE, USZipCode::DEFAULT_CITY_TYPE)
                    )->join(USLocation::TABLE . ' as city_locations', fn(JoinClause $join) =>
                        $join->on('city_locations.' . USLocation::FIELD_CITY_KEY, 'zip_locations.' . USLocation::FIELD_CITY_KEY)
                            ->on('city_locations.' . USLocation::FIELD_COUNTY_KEY, 'zip_locations.' . USLocation::FIELD_COUNTY_KEY)
                            ->on('city_locations.' . USLocation::FIELD_STATE_ABBREVIATION, 'zip_locations.' . USLocation::FIELD_STATE_ABBREVIATION)
                            ->where('city_locations.' . USLocation::FIELD_TYPE, USLocation::TYPE_CITY)
                    )->join(USLocation::TABLE . ' as county_locations', fn(JoinClause $join) =>
                        $join->on('county_locations.' . USLocation::FIELD_COUNTY_KEY, 'zip_locations.' . USLocation::FIELD_COUNTY_KEY)
                            ->on('county_locations.' . USLocation::FIELD_STATE_ABBREVIATION, 'zip_locations.' . USLocation::FIELD_STATE_ABBREVIATION)
                            ->where('county_locations.' . USLocation::FIELD_TYPE, USLocation::TYPE_COUNTY)
                    )->whereRaw("(3959 * acos(cos(radians(central_zip_code.latitude)) * cos(radians(us_zip_codes.latitude))
                              * cos(radians(us_zip_codes.longitude) - radians(central_zip_code.longitude)) +
                            sin(radians(central_zip_code.latitude)) * sin(radians(us_zip_codes.latitude))) < IFNULL(" . ServiceRadius::TABLE . '.' . ServiceRadius::FIELD_RADIUS . ", 30))")
                    ->orderBy('company_location_id')
                    ->whereIn(CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_ID, $ids);

                $now = now();
                $insertData = [];
                $grouped = $query->get()->groupBy(['company_id', 'company_location_id', 'state_location_id', 'county_location_id']);
                foreach($grouped as $companyId => $company) {
                    foreach ($company as $companyLocationId => $companyLocation) {
                        foreach ($companyLocation as $stateLocationId => $stateLocation) {
                            $insertData[] = [
                                NonPurchasingCompanyLocation::FIELD_COMPANY_LOCATION_ID => $companyLocationId,
                                NonPurchasingCompanyLocation::FIELD_COMPANY_ID          => $companyId,
                                NonPurchasingCompanyLocation::FIELD_LOCATION_ID         => $stateLocationId,
                                NonPurchasingCompanyLocation::FIELD_IS_SET_BY_RADIUS    => true,
                                NonPurchasingCompanyLocation::CREATED_AT                => $now,
                            ];
                            foreach($stateLocation as $countyLocationId => $countyLocation) {
                                $insertData[] = [
                                    NonPurchasingCompanyLocation::FIELD_COMPANY_LOCATION_ID => $companyLocationId,
                                    NonPurchasingCompanyLocation::FIELD_COMPANY_ID          => $companyId,
                                    NonPurchasingCompanyLocation::FIELD_LOCATION_ID         => $countyLocationId,
                                    NonPurchasingCompanyLocation::FIELD_IS_SET_BY_RADIUS    => true,
                                    NonPurchasingCompanyLocation::CREATED_AT                => $now,
                                ];
                                foreach($countyLocation as $cityLocation) {
                                    $insertData[] = [
                                        NonPurchasingCompanyLocation::FIELD_COMPANY_LOCATION_ID => $companyLocationId,
                                        NonPurchasingCompanyLocation::FIELD_COMPANY_ID          => $companyId,
                                        NonPurchasingCompanyLocation::FIELD_LOCATION_ID         => $cityLocation->city_location_id,
                                        NonPurchasingCompanyLocation::FIELD_IS_SET_BY_RADIUS    => true,
                                        NonPurchasingCompanyLocation::CREATED_AT                => $now,
                                    ];
                                }
                            }
                        }
                    }
                }

                $insertChunks = array_chunk($insertData, $this->insertLimit);
                    foreach($insertChunks as $insertChunk) {
                        if (!DB::table(NonPurchasingCompanyLocation::TABLE)->insert($insertChunk))
                            return false;
                    }

                $this->progressBar->advance();
                return true;
            });
    }
}